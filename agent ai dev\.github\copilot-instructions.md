<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

- This is a monorepo project containing:
  - VS Code extension (agent-developer-extension)
  - Desktop app (agent-developer-desktop, Vite + Electron)
  - Shared logic (shared)
- Please use the get_vscode_api tool for any VS Code API queries.
- The shared folder contains all business logic, API integrations, and localization.
- The project must support Arabic and English, and be easily extensible for new AI providers.
- Focus on modularity and code reuse between extension and desktop app.
